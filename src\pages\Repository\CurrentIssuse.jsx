import React from "react";
import "../../styles/CurrentIssue.css";

export default function CurrentIssue() {
  const articles = [
    {
      title: "EXPLORING THE IMPACT OF IOT ON THE DEVELOPMENT OF SMART CITIES",
      authors: "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Dr<PERSON> <PERSON><PERSON><PERSON>",
      pdf: "/assets/current/Aastha Bhatia.pdf",
      pages: "1-15",
      doi: "10.1234/lldjps.2025.v12i1.001"
    },
    {
      title: "LEVERAGING ARTIFICIAL INTELLIGENCE: A COMPREHENSIVE ANALYSIS OF ETHICAL CONSIDERATIONS OF AI FOR MARKETING STRATEGIES FOR HOSPITALS IN DELHI-NCR",
      authors: "<PERSON><PERSON><PERSON><PERSON><PERSON>, Dr. <PERSON><PERSON>",
      pdf: "/assets/current/Abhis<PERSON><PERSON>.pdf",
      pages: "16-28",
      doi: "10.1234/lldjps.2025.v12i1.002"
    },
    {
      title: "SECULARISM IN INDIA AND ITS LEGAL PERSPECTIVE",
      authors: "<PERSON><PERSON>, <PERSON><PERSON><PERSON>",
      pdf: "/assets/current/Am<PERSON>.pdf",
      pages: "29-42",
      doi: "10.1234/lldjps.2025.v12i1.003"
    },
    {
      title: "A STUDY ON AWARENESS OF GREEN HRM PRACTICES AMONG THE EMPLOYEES OF THE IT INDUSTRY",
      authors: "Kashish Jain",
      pdf: "/assets/current/Kashish Jain.pdf",
      pages: "43-56",
      doi: "10.1234/lldjps.2025.v12i1.004"
    },
    {
      title: "LIBRARY: A TOOL FOR THE UPLIFTMENT OF AN UNTOUCHED SOCIETY: ROLE OF AI",
      authors: "Murari Kumar",
      pdf: "/assets/current/Murari Kumar.pdf",
      pages: "57-68",
      doi: "10.1234/lldjps.2025.v12i1.005"
    },
    {
      title: "ASSESSING THE RELATIONSHIP BETWEEN LEADERSHIP STYLE & ORGANIZATIONAL CITIZENSHIP BEHAVIOR AMONG EMPLOYEES OF RWANDA ENERGY GROUP LIMITED",
      authors: "Prof. (Dr.) Satyendra Narayan Singh, Ms Dorothy Katushabe Kabanyane",
      pdf: "/assets/current/Satyendra Narayan Singh.pdf",
      pages: "69-84",
      doi: "10.1234/lldjps.2025.v12i1.006"
    },
    {
      title: "LEADERSHIP AND ENTREPRENEURSHIP / YOUTH ENTERPRISE START-UPS",
      authors: "Sharmishtha",
      pdf: "/assets/current/Sharmishtha.pdf",
      pages: "85-96",
      doi: "10.1234/lldjps.2025.v12i1.007"
    },
  ];

  return (
    <div className="current-issue-container min-h-screen bg-gradient-to-br from-gray-50 to-white py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Journal Header */}
        <div className="text-center mb-16">
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              LLDJPS - LINGAYA'S LALITA DEVI JOURNAL OF PROFESSIONAL STUDIES
            </h1>
            <div className="w-32 h-1 bg-gradient-to-r from-[#ea7717] to-[#ff8c42] mx-auto my-6 rounded-full"></div>
            <div className="bg-gradient-to-r from-[#ea7717] to-[#ff8c42] text-white px-8 py-4 rounded-full inline-block">
              <p className="text-xl md:text-2xl font-semibold">
                VOLUME 12, ISSUE 1 — JANUARY 2025
              </p>
            </div>
            <div className="mt-6 flex flex-wrap justify-center gap-4 text-sm text-gray-600">
              <span className="bg-gray-100 px-3 py-1 rounded-full">ISSN: 2230-987X</span>
              <span className="bg-gray-100 px-3 py-1 rounded-full">Peer Reviewed</span>
              <span className="bg-gray-100 px-3 py-1 rounded-full">Open Access</span>
            </div>
          </div>
        </div>

        {/* Articles List */}
        <div className="space-y-8">
          {articles.map((article, index) => (
            <div key={index} className="article-card bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100">
              <div className="p-6 md:p-8">
                <div className="flex flex-col lg:flex-row items-start gap-6">
                  {/* Article Number */}
                  <div className="flex-shrink-0">
                    <div className="article-number w-16 h-16 bg-gradient-to-br from-[#ea7717] to-[#ff8c42] rounded-full flex items-center justify-center">
                      <span className="text-white text-xl font-bold">
                        {String(index + 1).padStart(2, '0')}
                      </span>
                    </div>
                  </div>

                  {/* Article Content */}
                  <div className="flex-1 min-w-0">
                    <a
                      href={article.pdf}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group block"
                    >
                      <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-4 group-hover:text-[#ea7717] transition-colors duration-300 leading-tight">
                        {article.title}
                        <span className="ml-3 inline-block text-[#ea7717] opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </span>
                      </h2>
                    </a>

                    <div className="space-y-3">
                      <p className="text-gray-700 text-base">
                        <span className="font-semibold text-gray-900">Authors: <AUTHORS>
                        <span className="italic">{article.authors}</span>
                      </p>

                        
                        
                      </div>
                    </div>
                  </div>

                  {/* Download Button */}
                  <div className="flex-shrink-0">
                    <a
                      href={article.pdf}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="download-button inline-flex items-center gap-2 bg-gradient-to-r from-[#ea7717] to-[#ff8c42] text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Download PDF
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

       

   
      </div>
    </div>
  );
}
