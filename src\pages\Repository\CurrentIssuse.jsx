import React, { useState, useEffect } from "react";
import "../../styles/CurrentIssue.css";
import { CURRENT_ISSUE_DATA, RESEARCH_AREAS, JOURNAL_INFO } from "../../constants/currentIssueData";

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('CurrentIssue Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white py-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Something went wrong</h1>
            <p className="text-gray-600">Please refresh the page to try again.</p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

function CurrentIssueContent() {
  // Use persistent data from constants
  const articles = CURRENT_ISSUE_DATA.articles;
  const issueInfo = CURRENT_ISSUE_DATA;

  // Add loading state to ensure component mounts properly
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Ensure component is fully loaded
    setIsLoaded(true);
  }, []);

  // Show loading state if not loaded
  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#ea7717] mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading Current Issue...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="current-issue-container min-h-screen bg-gradient-to-br from-gray-50 to-white py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Journal Header */}
        <div className="text-center mb-16">
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {JOURNAL_INFO.fullName}
            </h1>
            <div className="w-32 h-1 bg-gradient-to-r from-[#ea7717] to-[#ff8c42] mx-auto my-6 rounded-full"></div>
            <div className="bg-gradient-to-r from-[#ea7717] to-[#ff8c42] text-white px-8 py-4 rounded-full inline-block">
              <p className="text-xl md:text-2xl font-semibold">
                VOLUME {CURRENT_ISSUE_DATA.volume}, ISSUE {CURRENT_ISSUE_DATA.issue} — {CURRENT_ISSUE_DATA.month.toUpperCase()} {CURRENT_ISSUE_DATA.year}
              </p>
            </div>
            <div className="mt-6 flex flex-wrap justify-center gap-4 text-sm text-gray-600">
              <span className="bg-gray-100 px-3 py-1 rounded-full">ISSN: {CURRENT_ISSUE_DATA.issn}</span>
              <span className="bg-gray-100 px-3 py-1 rounded-full">Peer Reviewed</span>
              <span className="bg-gray-100 px-3 py-1 rounded-full">Open Access</span>
            </div>
          </div>
        </div>

        {/* Articles List */}
        <div className="space-y-8">
          {articles.map((article, index) => (
            <div key={index} className="article-card bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100">
              <div className="p-6 md:p-8">
                <div className="flex flex-col lg:flex-row items-start gap-6">
                  {/* Article Number */}
                  <div className="flex-shrink-0">
                    <div className="article-number w-16 h-16 bg-gradient-to-br from-[#ea7717] to-[#ff8c42] rounded-full flex items-center justify-center">
                      <span className="text-white text-xl font-bold">
                        {String(index + 1).padStart(2, '0')}
                      </span>
                    </div>
                  </div>

                  {/* Article Content */}
                  <div className="flex-1 min-w-0">
                    <a
                      href={article.pdf}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group block"
                    >
                      <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-4 group-hover:text-[#ea7717] transition-colors duration-300 leading-tight">
                        {article.title}
                        <span className="ml-3 inline-block text-[#ea7717] opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </span>
                      </h2>
                    </a>

                    <div className="space-y-3">
                      <p className="text-gray-700 text-base">
                        <span className="font-semibold text-gray-900">Authors: <AUTHORS>
                        <span className="italic">{article.authors}</span>
                      </p>

                      <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          Pages: {article.pages}
                        </span>
                        <span className="flex items-center gap-1">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                          </svg>
                          DOI: {article.doi}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Download Button */}
                  <div className="flex-shrink-0">
                    <a
                      href={article.pdf}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="download-button inline-flex items-center gap-2 bg-gradient-to-r from-[#ea7717] to-[#ff8c42] text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Download PDF
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* About Section */}
        <div className="mt-16 bg-white rounded-xl shadow-lg p-8 md:p-12 border border-gray-100">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              ABOUT THIS ISSUE
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-[#ea7717] to-[#ff8c42] mx-auto rounded-full"></div>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Editorial Information</h3>
                <div className="space-y-2 text-gray-700">
                  <p><span className="font-medium">Publication Date:</span> {CURRENT_ISSUE_DATA.month} {CURRENT_ISSUE_DATA.year}</p>
                  <p><span className="font-medium">Total Articles:</span> {articles.length}</p>
                  <p><span className="font-medium">Review Process:</span> {JOURNAL_INFO.reviewProcess}</p>
                  <p><span className="font-medium">Publication Type:</span> {JOURNAL_INFO.accessType}</p>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Research Areas</h3>
                <div className="flex flex-wrap gap-2">
                  {RESEARCH_AREAS.map((area, index) => (
                    <span key={index} className="bg-gradient-to-r from-[#ea7717] to-[#ff8c42] text-white px-3 py-1 rounded-full text-sm font-medium">
                      {area}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">About LLDJPS</h3>
              <p className="text-gray-700 leading-relaxed mb-4">
                This issue of LLDJPS features peer-reviewed research articles from diverse fields
                including technology, business, health, and social sciences. Each article has
                undergone a rigorous double-blind review process to ensure high academic quality
                and relevance for professionals and researchers.
              </p>
              <p className="text-gray-700 leading-relaxed">
                The journal continues to serve as a platform for scholarly discourse and knowledge
                dissemination in professional studies, fostering innovation and academic excellence
                across multiple disciplines.
              </p>
            </div>
          </div>
        </div>

        {/* Citation Information */}
        <div className="mt-8 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">How to Cite This Issue</h3>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <p className="text-sm text-gray-700 font-mono">
              LLDJPS - Lingaya's Lalita Devi Journal of Professional Studies, Volume 12, Issue 1, January 2025.
              ISSN: 2230-987X. Available at: https://lldjps.lldims.edu.in/repository/current
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Main export with Error Boundary
export default function CurrentIssue() {
  return (
    <ErrorBoundary>
      <CurrentIssueContent />
    </ErrorBoundary>
  );
}
