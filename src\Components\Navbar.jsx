import React, { useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import logo from '../assets/LLDIMS Logo Circle (2).png';
import ccLogo from '../assets/Images/Navbar.jpg'; // renamed for clarity

const Navbar = () => {
  const [openDropdown, setOpenDropdown] = useState(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const dropdownTimeout = useRef(null);

  const handleMouseEnter = (menu) => {
    clearTimeout(dropdownTimeout.current);
    setOpenDropdown(menu);
  };

  const handleMouseLeave = () => {
    dropdownTimeout.current = setTimeout(() => {
      setOpenDropdown(null);
    }, 120);
  };

  const toggleMobileMenu = () => setIsMobileMenuOpen(!isMobileMenuOpen);
  const handleNavLinkClick = () => {
    setOpenDropdown(null);
    setIsMobileMenuOpen(false);
  };

  return (
    <nav className="bg-brand-secondary text-white shadow-lg sticky top-0 z-30">
      <div className="max-w-7xl mx-auto px-2 sm:px-4">
        <div className="flex justify-between items-center h-16 sm:h-20">
          {/* Logo Section */}
          <Link to="/" className="flex items-center space-x-3 group">
            <img
              src={logo}
              alt="LLDIMS Logo"
              className="h-10 w-10 sm:h-12 sm:w-12 rounded-full border-2 border-brand-primary group-hover:border-brand-primary-light transition-colors"
            />
            <div className="flex flex-col">
              <span className="text-lg sm:text-xl font-bold text-brand-primary group-hover:text-brand-primary-light transition-colors">
                LLDJPS
              </span>
              <span className="text-xs sm:text-sm text-gray-300 font-medium">
                ISSN 2230-987X
              </span>
            </div>
          </Link>

          {/* Hamburger for Mobile */}
          <button
            onClick={toggleMobileMenu}
            className="md:hidden p-3 rounded-lg bg-brand-primary/10 hover:bg-brand-primary/20 focus:outline-none focus:ring-2 focus:ring-brand-primary transition-all duration-200 border border-brand-primary/30"
          >
            {isMobileMenuOpen ? (
              <X size={24} className="text-brand-primary" />
            ) : (
              <Menu size={24} className="text-brand-primary" />
            )}
          </button>

          {/* Desktop Menu */}
          <ul className="hidden md:flex space-x-4 lg:space-x-8 items-center ml-auto">
            <li>
              <Link
                to="/"
                className="hover:text-brand-primary-light px-2 py-1 rounded transition-colors"
                onClick={handleNavLinkClick}
              >
                Home
              </Link>
            </li>

            {/* About */}
            <li
              onMouseEnter={() => handleMouseEnter('about')}
              onMouseLeave={handleMouseLeave}
              className="relative"
            >
              <span className="cursor-pointer hover:text-brand-primary-light px-2 py-1 rounded transition-colors">
                About
              </span>
              {openDropdown === 'about' && (
                <DropdownMenu
                  links={[
                    { label: 'About the Journal', to: '/aboutus/about-the-journal' },
                    { label: 'Aim and Scope', to: '/aboutus/aim-and-scope' },
                    { label: 'Guidelines for Manuscript', to: '/aboutus/guidelines-for-manuscript' },
                    { label: 'Submission Procedure', to: '/aboutus/submission-procedure' },
                  ]}
                  onLinkClick={handleNavLinkClick}
                />
              )}
            </li>

            {/* Editorial */}
            <li
              onMouseEnter={() => handleMouseEnter('editorial')}
              onMouseLeave={handleMouseLeave}
              className="relative"
            >
              <span className="cursor-pointer hover:text-brand-primary-light px-2 py-1 rounded transition-colors">
                Editorial
              </span>
              {openDropdown === 'editorial' && (
                <DropdownMenu
                  links={[
                    { label: 'Editor in Chief', to: '/editorialOrganization/editoral-in-chife' },
                    { label: 'Editorial Board Members', to: '/editorialOrganization/editoral-Board-Members' },
                    { label: 'Associate & Executive Editors', to: '/editorialOrganization/AssociateAndExecutive' },
                    { label: 'Join as Reviewer', to: '/editorialOrganization/join-reviewer' },
                  ]}
                  onLinkClick={handleNavLinkClick}
                />
              )}
            </li>

            {/* Policies */}
            <li
              onMouseEnter={() => handleMouseEnter('policies')}
              onMouseLeave={handleMouseLeave}
              className="relative"
            >
              <span className="cursor-pointer hover:text-brand-primary-light px-2 py-1 rounded transition-colors">
                Policies
              </span>
              {openDropdown === 'policies' && (
                <DropdownMenu
                  links={[
                    { label: 'Copyright Policy', to: '/policies/copyright-policy' },
                    { label: 'Ethics', to: '/policies/ethics' },
                    { label: 'Open Access Policy', to: '/policies/open-access-policy' },
                    { label: 'Peer Review Policy', to: '/policies/peer-Review-policy' },
                  ]}
                  onLinkClick={handleNavLinkClick}
                />
              )}
            </li>

            {/* Repository */}
            <li
              onMouseEnter={() => handleMouseEnter('repository')}
              onMouseLeave={handleMouseLeave}
              className="relative"
            >
              <span className="cursor-pointer hover:text-brand-primary-light px-2 py-1 rounded transition-colors">
                Repository
              </span>
              {openDropdown === 'repository' && (
                <DropdownMenu
                  links={[
                    { label: 'Current Issue', to: '/repository/current' },
                    { label: 'Archive Articles', to: '/repository/archive-articles' },
                    { label: 'Books Published', to: '/repository/books-published' },
                  ]}
                  onLinkClick={handleNavLinkClick}
                />
              )}
            </li>

            {/* Contact */}
            <li>
              <Link
                to="/contact"
                className="hover:text-brand-primary-light px-2 py-1 rounded transition-colors"
                onClick={handleNavLinkClick}
              >
                Contact Us
              </Link>
            </li>
          </ul>

          {/* Rightmost CC Logo */}
          <img
            src={ccLogo}
            alt="Creative Commons BY-NC-SA License"
            className="h-8   min-w-[100px]"
          />
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden absolute top-full left-0 right-0 bg-white shadow-2xl border-t-2 border-brand-primary z-40 animate-fade-in-down">
            <div className="flex flex-col py-4 px-4 space-y-1">
              <Link
                to="/"
                className="py-3 px-4 text-brand-text font-semibold hover:bg-brand-primary/10 hover:text-brand-primary rounded-lg transition-all duration-200 border-b border-gray-100"
                onClick={handleNavLinkClick}
              >
                🏠 Home
              </Link>

              <DropdownMobile
                title="About"
                links={[
                  { label: 'About the Journal', to: '/aboutus/about-the-journal' },
                  { label: 'Aim and Scope', to: '/aboutus/aim-and-scope' },
                  { label: 'Guidelines for Manuscript', to: '/aboutus/guidelines-for-manuscript' },
                  { label: 'Submission Procedure', to: '/aboutus/submission-procedure' },
                ]}
                onLinkClick={handleNavLinkClick}
              />

              <DropdownMobile
                title="Editorial"
                links={[
                  { label: 'Editor in Chief', to: '/editorialOrganization/editoral-in-chife' },
                  { label: 'Editorial Board Members', to: '/editorialOrganization/editoral-Board-Members' },
                  { label: 'Associate & Executive Editors', to: '/editorialOrganization/AssociateAndExecutive' },
                  { label: 'Advisory Board', to: '/editorialOrganization/AdvisoryBoard' },
                  { label: 'Join as Reviewer', to: '/editorialOrganization/join-reviewer' },
                ]}
                onLinkClick={handleNavLinkClick}
              />

              <DropdownMobile
                title="Policies"
                links={[
                  { label: 'Copyright Policy', to: '/policies/copyright-policy' },
                  { label: 'Ethics', to: '/policies/ethics' },
                  { label: 'Open Access Policy', to: '/policies/open-access-policy' },
                  { label: 'Peer Review Policy', to: '/policies/peer-Review-policy' },
                ]}
                onLinkClick={handleNavLinkClick}
              />

              <DropdownMobile
                title="Repository"
                links={[
                  { label: 'Current Issue', to: '/repository/current' },
                  { label: 'Archive Articles', to: '/repository/archive-articles' },
                  { label: 'Books Published', to: '/repository/books-published' },
                ]}
                onLinkClick={handleNavLinkClick}
              />

              <Link
                to="/contact"
                className="py-3 px-4 text-brand-text font-semibold hover:bg-brand-primary/10 hover:text-brand-primary rounded-lg transition-all duration-200"
                onClick={handleNavLinkClick}
              >
                📞 Contact Us
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

// Dropdown for Desktop
const DropdownMenu = ({ links, onLinkClick }) => (
  <ul className="absolute top-full left-1/2 -translate-x-1/2 mt-2 bg-white text-brand-text rounded shadow-lg min-w-[14rem] z-50 text-center py-2">
    {links.map((item, idx) => (
      <li key={idx} className="px-4 py-2 hover:bg-gray-100">
        <Link to={item.to} onClick={onLinkClick}>
          {item.label}
        </Link>
      </li>
    ))}
  </ul>
);

// Dropdown for Mobile
const DropdownMobile = ({ title, links, onLinkClick }) => {
  const [isOpen, setIsOpen] = useState(false);

  const getIcon = (title) => {
    switch (title) {
      case 'About':
        return '📖';
      case 'Editorial':
        return '👥';
      case 'Policies':
        return '📋';
      case 'Repository':
        return '📚';
      default:
        return '📄';
    }
  };

  return (
    <div className="border-b border-gray-100">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full py-3 px-4 text-left font-semibold text-brand-text hover:bg-brand-primary/10 hover:text-brand-primary rounded-lg transition-all duration-200 flex items-center justify-between"
      >
        <span className="flex items-center space-x-2">
          <span>{getIcon(title)}</span>
          <span>{title}</span>
        </span>
        <span
          className={`transform transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        >
          ▼
        </span>
      </button>
      {isOpen && (
        <div className="pl-6 pb-2 space-y-1">
          {links.map((item, idx) => (
            <Link
              key={idx}
              to={item.to}
              className="block py-2 px-3 text-sm text-gray-600 hover:text-brand-primary hover:bg-brand-primary/5 rounded transition-all duration-200"
              onClick={onLinkClick}
            >
              • {item.label}
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

export default Navbar;
