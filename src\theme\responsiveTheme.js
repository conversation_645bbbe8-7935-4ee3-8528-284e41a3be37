import { createTheme, responsiveFontSizes } from '@mui/material/styles';

// Custom Color Palette based on #EA7717
const customPalette = {
  // Primary Orange Colors (based on #EA7717)
  orange: {
    50: '#fff8f1',
    100: '#feecdc',
    200: '#fcd9bd',
    300: '#fdba8c',
    400: '#ff8a4c',
    500: '#EA7717', // Main brand color
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a'
  },
  // Complementary Dark Colors (for contrast)
  dark: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
    950: '#020617'
  },
  // Neutral Colors
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717'
  }
};

// Create a responsive theme with #EA7717 as primary color
let theme = createTheme({
  palette: {
    primary: {
      main: '#EA7717', // Main brand color
      light: customPalette.orange[400], // Lighter orange
      dark: customPalette.orange[700], // Darker orange
      contrastText: '#ffffff'
    },
    secondary: {
      main: customPalette.dark[800], // Dark complement
      light: customPalette.dark[600], // Lighter dark
      dark: customPalette.dark[900], // Darker complement
      contrastText: '#ffffff'
    },
    background: {
      default: '#ffffff', // Pure white background
      paper: customPalette.neutral[50], // Very light gray for cards
    },
    text: {
      primary: customPalette.dark[900], // Dark text
      secondary: customPalette.neutral[600], // Gray secondary text
    },
    error: {
      main: '#dc2626',
      light: '#ef4444',
      dark: '#b91c1c'
    },
    warning: {
      main: '#EA7717',
      light: customPalette.orange[400],
      dark: customPalette.orange[700]
    },
    info: {
      main: customPalette.dark[600],
      light: customPalette.dark[400],
      dark: customPalette.dark[800]
    },
    success: {
      main: '#16a34a',
      light: '#22c55e',
      dark: '#15803d'
    },
    divider: customPalette.neutral[200],
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem',
    },
    h2: {
      fontWeight: 600,
      fontSize: '2rem',
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.25rem',
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
  },
  components: {
    MuiContainer: {
      styleOverrides: {
        root: {
          paddingLeft: '16px',
          paddingRight: '16px',
          '@media (min-width:600px)': {
            paddingLeft: '24px',
            paddingRight: '24px',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          transition: 'box-shadow 0.3s ease-in-out',
          '&:hover': {
            boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
          },
        },
      },
    },
  },
});

// Apply responsive font sizes
theme = responsiveFontSizes(theme);

export default theme;