import './App.css'
import './styles/global.css'
import { Route, Routes, Navigate } from 'react-router-dom'
import ReviewersPanel from './pages/EditorialOraganization/ReviewersPanel'
import Join<PERSON><PERSON><PERSON><PERSON><PERSON> from './pages/EditorialOraganization/Join<PERSON><PERSON><PERSON>ie<PERSON>'
import AdvisoryBoard from './pages/EditorialOraganization/AdvisoryBoard'
import BookPublished from './pages/Repository/BookPublished'
import ArchiveArticle from './pages/Repository/ArchiveArticle'
import AimAndScope from './pages/AboutUs/AimAndScope'
import HomeNew from './pages/HomeNew'
import SubmitArticle from './pages/SubmitArticle'
import ContactUs from './pages/Contactus'
import EditorialProcess from './pages/AboutUs/EditorialProcess'
import PublisherInfo from './pages/AboutUs/PublisherInfo'
import PublicationPolicies from './pages/policies/PublicationPolicies'
import Ethics from './pages/policies/Ethics'
import OtherGuidelines from './pages/policies/OtherGuidelines'
import CancellationRefund from './pages/policies/Cancellation-refund'
import Plagiarism from './pages/policies/Plagiarism'
import Layout from './Components/MainLayout'
import CurrentIssus from './pages/Repository/CurrentIssuse'
import SubmissionProcedure from './pages/AboutUs/SubmissionProcedure'
import CopyrightPolicies from './pages/policies/Copyrightpolicies'
import OpenAccess from './pages/policies/OpenAccess'
import PeerReviewPolicy from './pages/policies/PeerReviewPolicy'
import GuidelinesforManuscript from './pages/AboutUs/GuidelinesforManuscript'
import EditorInChief from './pages/EditorialOraganization/EditorialBoard/EditorInChief'
import EditorialBoardMembers from './pages/EditorialOraganization/EditorialBoard/EditorialBoardMembers'
import AssociateAndExecutiveEditors from './pages/EditorialOraganization/EditorialBoard/AssociateAndExecutiveEditors'
import AboutTheJournal from './pages/AboutUs/AbouttheJournal'
// Import other components as needed

function App() {
  return (
    <Routes>
      {/* All routes are wrapped in MainLayout for consistent layout */}
      <Route path='/' element={<Layout />}>
        <Route index element={<HomeNew/>} />
        
        {/* Editorial Organization */}
            <Route path='/editorialOrganization/editoral-in-chife' element={<EditorInChief/>} />
            <Route path='/editorialOrganization/editoral-Board-Members' element={<EditorialBoardMembers/>} />
            <Route path='/editorialOrganization/AssociateAndExecutive' element={<AssociateAndExecutiveEditors/>} />
        <Route path='/editorialOrganization/reviewers-panel' element={<ReviewersPanel />} />
        <Route path='/editorialOrganization/join-reviewer' element={<JoinAsReviewer />} />
        
        {/* Repository */}
        <Route path='/repository/current' element={<Layout><CurrentIssus /></Layout>} />
        <Route path='/repository/archive-articles' element={<ArchiveArticle />} />
        <Route path='/repository/bookpublished' element={<BookPublished />} />
        
        {/* About Us */}
        <Route path='/aboutus/about-the-journal' element={<AboutTheJournal/>} />
        <Route path='/aboutus/aim-and-scope' element={<AimAndScope />} />
        <Route path='/aboutus/Submission-Procedure' element={<SubmissionProcedure />} />
        <Route path='/aboutus/guidelines-for-manuscript' element={<GuidelinesforManuscript/>} />
        <Route path='/aboutus/editorial-process' element={<EditorialProcess/>} />
        <Route path='/aboutus/publisher-info' element={<PublisherInfo />} />

        
        {/* Submit Article */}
        <Route path='/submit-article' element={<SubmitArticle />} />
        
        {/* Policies - Add placeholder components for now */}
        <Route path='/policies/copyright-policy' element={<CopyrightPolicies />} />
        <Route path='/policies/open-access-policy' element={<OpenAccess />} />
        <Route path='/policies/ethics' element={ <Ethics/>} />
        <Route path='/policies/peer-Review-policy' element={<PeerReviewPolicy />} />

{/* not in use */}
        <Route path='/policies/publication-policies' element={<PublicationPolicies />} />
        <Route path='/policies/guidelines' element={<OtherGuidelines /> } />
        <Route path='/policies/cancellation-refund' element={<CancellationRefund  /> } />
        <Route path='/policies/plagiarism' element={<Plagiarism />} />
        
        {/* Contact Us page */}
        <Route path='/contact' element={<ContactUs />} />
        {/* Catch all route - redirect to home */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Route>
    </Routes>
  );
}

export default App
