import React, { useState } from 'react';
import {
  Container,
  Typography,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Box,
  Grid,
  TextField,
  MenuItem,
  Button,
  Divider,
  FormControlLabel,
  Checkbox,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import {
  CloudUpload,
  Check as CheckIcon,
  Info as InfoIcon,
  ArrowForward,
  Description,
} from '@mui/icons-material';
import Navbar from '../Components/Navbar';
import Footer from '../Components/Footer';

const SubmitArticle = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    title: '',
    abstract: '',
    keywords: '',
    category: '',
    authors: [{ name: '', email: '', affiliation: '', corresponding: false }],
    agreeToTerms: false,
    files: {
      manuscript: null,
      coverLetter: null,
    },
  });

  const steps = ['Author Info', 'Manuscript', 'Upload', 'Review'];

  const handleNext = () => setActiveStep((prevStep) => prevStep + 1);
  const handleBack = () => setActiveStep((prevStep) => prevStep - 1);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleAuthorChange = (index, field, value) => {
    const updatedAuthors = [...formData.authors];
    updatedAuthors[index] = { ...updatedAuthors[index], [field]: value };
    setFormData({ ...formData, authors: updatedAuthors });
  };

  const addAuthor = () => {
    setFormData({
      ...formData,
      authors: [
        ...formData.authors,
        { name: '', email: '', affiliation: '', corresponding: false },
      ],
    });
  };

  const removeAuthor = (index) => {
    const updatedAuthors = [...formData.authors];
    updatedAuthors.splice(index, 1);
    setFormData({ ...formData, authors: updatedAuthors });
  };

  const handleFileChange = (type, file) => {
    setFormData({
      ...formData,
      files: { ...formData.files, [type]: file },
    });
  };

  const handleSubmit = () => setActiveStep(steps.length);

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Author Information
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              Specify all authors. Mark corresponding author clearly.
            </Alert>

            {formData.authors.map((author, i) => (
              <Paper
                elevation={1}
                sx={{ p: 2, mb: 2, borderRadius: 2, backgroundColor: '#fafafa' }}
                key={i}
              >
                <Typography fontWeight="bold" color="primary">
                  Author {i + 1}
                </Typography>
                <Grid container spacing={2} mt={1}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      value={author.name}
                      onChange={(e) => handleAuthorChange(i, 'name', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Email"
                      value={author.email}
                      onChange={(e) => handleAuthorChange(i, 'email', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Affiliation"
                      value={author.affiliation}
                      onChange={(e) => handleAuthorChange(i, 'affiliation', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={author.corresponding}
                          onChange={(e) =>
                            handleAuthorChange(i, 'corresponding', e.target.checked)
                          }
                        />
                      }
                      label="Corresponding Author"
                    />
                  </Grid>
                </Grid>
                {i > 0 && (
                  <Button
                    color="error"
                    onClick={() => removeAuthor(i)}
                    sx={{ mt: 1 }}
                  >
                    Remove
                  </Button>
                )}
              </Paper>
            ))}
            <Button
              onClick={addAuthor}
              variant="outlined"
              startIcon={<InfoIcon />}
              sx={{ mt: 1 }}
            >
              Add Author
            </Button>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Manuscript Information
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={5}
                  label="Abstract"
                  name="abstract"
                  value={formData.abstract}
                  onChange={handleInputChange}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Keywords"
                  name="keywords"
                  value={formData.keywords}
                  onChange={handleInputChange}
                  helperText="Use commas to separate"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  select
                  fullWidth
                  label="Category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                >
                  {['Health', 'Technology', 'Education', 'Business'].map((cat) => (
                    <MenuItem key={cat} value={cat.toLowerCase()}>
                      {cat}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
            </Grid>
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Upload Files
            </Typography>
            {['manuscript', 'coverLetter'].map((type) => (
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  mt: 2,
                  borderRadius: 2,
                  backgroundColor: '#fafafa',
                }}
                key={type}
              >
                <Typography fontWeight="bold">
                  {type === 'manuscript' ? 'Manuscript File' : 'Cover Letter'}
                </Typography>
                <input
                  accept=".pdf,.doc,.docx"
                  style={{ display: 'none' }}
                  id={type}
                  type="file"
                  onChange={(e) => handleFileChange(type, e.target.files[0])}
                />
                <label htmlFor={type}>
                  <Button
                    variant="contained"
                    component="span"
                    startIcon={<CloudUpload />}
                    sx={{ mt: 1, '&:hover': { backgroundColor: '#0056b3' } }}
                  >
                    Upload
                  </Button>
                </label>
                {formData.files[type] && (
                  <Typography mt={1}>Selected: {formData.files[type].name}</Typography>
                )}
              </Paper>
            ))}
          </Box>
        );

      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review Submission
            </Typography>
            <Divider sx={{ my: 2 }} />
            <Paper sx={{ p: 2, mb: 2, borderRadius: 2 }}>
              <Typography fontWeight="bold">Title:</Typography>
              <Typography>{formData.title || 'Not Provided'}</Typography>
            </Paper>
            <Paper sx={{ p: 2, mb: 2, borderRadius: 2 }}>
              <Typography fontWeight="bold">Authors: <AUTHORS>
              {formData.authors.map((a, i) => (
                <Typography key={i} variant="body2">
                  {a.name} ({a.email}) — {a.affiliation}{' '}
                  {a.corresponding && '[Corresponding]'}
                </Typography>
              ))}
            </Paper>
            <Paper sx={{ p: 2, mb: 2, borderRadius: 2 }}>
              <Typography fontWeight="bold">Files:</Typography>
              <List>
                {['manuscript', 'coverLetter'].map((file) => (
                  <ListItem key={file}>
                    <ListItemIcon>
                      <Description />
                    </ListItemIcon>
                    <ListItemText
                      primary={file}
                      secondary={formData.files[file]?.name || 'Not uploaded'}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.agreeToTerms}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      agreeToTerms: e.target.checked,
                    }))
                  }
                />
              }
              label="I confirm this manuscript is original and unpublished."
            />
          </Box>
        );

      default:
        return (
          <Box textAlign="center">
            <CheckIcon color="success" sx={{ fontSize: 60, my: 2 }} />
            <Typography variant="h5" color="primary">
              Submission Complete!
            </Typography>
            <Typography sx={{ mb: 2 }}>
              Your submission ID is: IJAR-{Date.now().toString().slice(-4)}
            </Typography>
            <Button variant="contained" href="/">
              Return Home
            </Button>
          </Box>
        );
    }
  };

  return (
    <>
      <Container maxWidth="md" sx={{ py: 6 }}>
        <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
          <Typography
            variant="h4"
            align="center"
            gutterBottom
            fontWeight="bold"
            color="primary"
          >
            Submit Manuscript
          </Typography>
          <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
          {renderStepContent(activeStep)}
          {activeStep < steps.length && (
            <Box display="flex" justifyContent="space-between" mt={4}>
              <Button disabled={activeStep === 0} onClick={handleBack}>
                Back
              </Button>
              <Button
                variant="contained"
                onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}
                disabled={activeStep === steps.length - 1 && !formData.agreeToTerms}
                endIcon={activeStep === steps.length - 1 ? null : <ArrowForward />}
              >
                {activeStep === steps.length - 1 ? 'Submit' : 'Next'}
              </Button>
            </Box>
          )}
        </Paper>
      </Container>
    </>
  );
};

export default SubmitArticle;
