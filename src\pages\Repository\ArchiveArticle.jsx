import React, { useState } from 'react';
import { Container, Typography, <PERSON>, <PERSON>, CardContent, But<PERSON>, Tabs, Tab } from '@mui/material';
import { PictureAsPdf, Visibility } from '@mui/icons-material';

// Import PDFs
import volume10Issue1 from '../../assets/Archive Article/Volume-10-Issue-1-June-2023.pdf';
import volume10Issue2 from '../../assets/Archive Article/Volume-10, Issue-2, December 2023.pdf';
import volume11Issue1 from '../../assets/Archive Article/Volume-11-Issue-1-2024.pdf';
import volume11Issue2 from '../../assets/Archive Article/Volume-11-Issue-2-2024.pdf';
import volume9Issue1 from '../../assets/Archive Article/Volume-9-Issue-1-2022.pdf';

const ArchiveArticle = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  // Organize PDFs by year
  const archiveByYear = {
    2024: [
      {
        title: 'Volume 11, Issue 2',
        description: 'Research articles covering various domains of professional studies',
        pdf: volume11Issue2,
        fileName: 'Volume-11-Issue-2-2024.pdf'
      },
      {
        title: 'Volume 11, Issue 1',
        description: 'Peer-reviewed research in business, technology, and social sciences',
        pdf: volume11Issue1,
        fileName: 'Volume-11-Issue-1-2024.pdf'
      }
    ],
    2023: [
      {
        title: 'Volume 10, Issue 2',
        description: 'Scholarly contributions in professional studies and research',
        pdf: volume10Issue2,
        fileName: 'Volume-10-Issue-2-2023.pdf'
      },
      {
        title: 'Volume 10, Issue 1',
        description: 'June 2023 edition with multidisciplinary research articles',
        pdf: volume10Issue1,
        fileName: 'Volume-10-Issue-1-June-2023.pdf'
      }
    ],
    2022: [
      {
        title: 'Volume 9, Issue 1',
        description: 'Research articles from 2022 covering diverse academic fields',
        pdf: volume9Issue1,
        fileName: 'Volume-9-Issue-1-2022.pdf'
      }
    ]
  };

  const years = [2024, 2023, 2022];

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography
        variant="h3"
        component="h1"
        sx={{
          fontWeight: 700,
          color: '#EA7717',
          mb: 4,
          textAlign: 'left'
        }}
      >
        Archive Articles
      </Typography>

      <Typography
        variant="h6"
        sx={{
          color: '#64748b',
          mb: 6,
          textAlign: 'left'
        }}
      >
        Browse our collection of published research articles from previous issues
      </Typography>

      {/* Year Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
        <Tabs value={selectedTab} onChange={handleTabChange}>
          {years.map((year, index) => (
            <Tab key={year} label={year} />
          ))}
        </Tabs>
      </Box>

      {/* Tab Panels with responsive grid */}
      {years.map((year, index) => (
        <TabPanel key={year} value={selectedTab} index={index}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {archiveByYear[year].map((issue, issueIndex) => (
              <Card
                key={issueIndex}
                elevation={3}
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 25px rgba(234, 119, 23, 0.15)',
                  },
                  cursor: 'pointer'
                }}
                onClick={() => window.open(issue.pdf, '_blank')}
              >
                <CardContent sx={{ p: 3, flexGrow: 1, textAlign: 'left' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PictureAsPdf sx={{ fontSize: 40, color: '#EA7717', mr: 2 }} />
                    <Box>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: '#1e293b',
                          textAlign: 'left'
                        }}
                      >
                        {issue.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: '#64748b',
                          textAlign: 'left'
                        }}
                      >
                        {year}
                      </Typography>
                    </Box>
                  </Box>

                  <Typography
                    variant="body2"
                    sx={{
                      mb: 3,
                      color: '#475569',
                      textAlign: 'left',
                      flexGrow: 1
                    }}
                  >
                    {issue.description}
                  </Typography>

                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<Visibility />}
                    sx={{
                      bgcolor: '#EA7717',
                      '&:hover': { bgcolor: '#d66a14' },
                      textTransform: 'none',
                      mt: 'auto'
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(issue.pdf, '_blank');
                    }}
                  >
                    View PDF
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabPanel>
      ))}
    </Container>
  );
};

export default ArchiveArticle;
